import 'dart:convert';

import 'package:asset_force_mobile_v2/core/event_bus/event/overlay/overlay_filtertalk_change_event.dart';
import 'package:asset_force_mobile_v2/core/event_bus/event_bus.dart';
import 'package:asset_force_mobile_v2/core/js_engine/data/js_executor_context.dart';
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_view_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/services/asset_detail_service.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/get_asset_history_records_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/get_customize_logic_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/init_schedule_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/live_talk_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/load_asset_by_id_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/save_asset_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/models/asset_detail_arguments_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/states/asset_detail_ui_state.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/states/asset_relation_state.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_history/domain/entities/asset_history_delete_result.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_history/domain/entities/asset_history_source_enum.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_history/presentation/controllers/asset_history_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_schedule/domain/entity/asset_schedule_arugment.dart';
import 'package:asset_force_mobile_v2/features/overlay/overlay_page/presentation/models/filter_talk_parameter.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/appointment/appointment_list_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AssetDetailController extends BaseController with GetSingleTickerProviderStateMixin {
  // ==================== 常量定义 ====================

  /// Tab相关常量
  static const int _TAB_COUNT = 2;
  static const int _ASSET_INFO_TAB_INDEX = 0;
  static const int _HISTORY_TAB_INDEX = 1;

  /// 权限相关常量
  static const String _EDIT_PERMISSION_ENABLED = '1';

  /// 资产相关常量
  static const String _ASSET_TYPE_KEY = '資産種類';

  /// 对话框文本常量
  static const String _SAVE_CONFIRMATION_MESSAGE = '保存しますか？';
  static const String _CANCEL_TEXT = 'キャンセル';
  static const String _UPDATE_COMPLETE_MESSAGE = 'アップデート完了しました';
  static const String _OK_TEXT = 'OK';
  static const String _TAB_CHANGE_CONFIRMATION_TITLE = '確認';
  static const String _TAB_CHANGE_CONFIRMATION_MESSAGE = 'このタブに移動してもよろしいですか？';
  static const String _MOVE_TEXT = '移動';

  /// 数据同步策略相关常量
  static const bool _ENABLE_ASYNC_VERIFICATION = true; // 是否启用异步验证
  static const int _VERIFICATION_DELAY_MS = 1000; // 验证延迟时间（毫秒）
  static const bool _SHOW_VERIFICATION_ERRORS = false; // 是否显示验证错误

  // ==================== 私有方法 ====================

  /// 处理异常并更新UI状态
  ///
  /// [error] - 异常对象
  /// [errorMessage] - 自定义错误消息，如果为null则使用异常的toString()
  void _handleError(dynamic error, [String? errorMessage]) {
    final message = errorMessage ?? error.toString();
    LogUtil.e(message);
    uiState.isError.value = true;
    uiState.errorMessage.value = message;
  }

  /// 执行带Loading状态的异步操作
  ///
  /// [operation] - 要执行的异步操作
  /// [showLoadingDialog] - 是否显示Loading对话框
  /// [errorMessage] - 自定义错误消息前缀
  Future<void> _executeWithLoading(
    Future<void> Function() operation, {
    bool showLoadingDialog = true,
    String? errorMessage,
  }) async {
    try {
      if (showLoadingDialog) {
        showLoading();
      } else {
        uiState.isLoading.value = true;
      }
      await operation();
    } catch (e) {
      final message = errorMessage != null ? '$errorMessage: $e' : e.toString();
      _handleError(e, message);
    } finally {
      if (showLoadingDialog) {
        hideLoading();
      } else {
        uiState.isLoading.value = false;
      }
    }
  }

  // ==================== 属性定义 ====================

  /// 画面传入参数
  final AssetDetailArguments assetDetailArguments;

  // viewmodel
  final AssetDetailUiState uiState;

  // 用例相关
  final AssetDetailService assetDetailService;

  // 关联资产状态
  final AssetRelationState relationState = AssetRelationState();

  AssetDetailController({
    required this.uiState,
    required this.assetDetailService,
    required this.assetDetailArguments,
    required this.navigationService,
    required JsExecutorContext this.jsExecutorContext,
  }) {
    tabController = TabController(length: _TAB_COUNT, vsync: this);
  }

  /// 资产 id
  int assetId = 0;

  /// 资产种类 id
  int assetTypeId = 0;

  /// 资产种类名
  String assetTypeName = '';

  /// 资产明名
  String assetName = '';

  /// 资产 asset text
  String assetText = '';

  /// 跳转来自
  late final AssetDetailSource fromScanPage = assetDetailArguments.fromScanPage;

  /// カスタマイズロジック
  Rx<GetCustomizeLogicResult?> jsCustomizeLogic = Rx<GetCustomizeLogicResult?>(null);

  /// 资产对象
  Asset? asset;

  /// 更新日时
  String modifiedDate = '';

  /// 标签控制器
  late TabController tabController;

  /// 导航服务
  final NavigationService navigationService;

  /// Js执行器上下文
  JsExecutorContext jsExecutorContext;

  @override
  void onInit() {
    super.onInit();
    configureNavigationBarVisibility();
  }

  @override
  bool shouldShowNavigationBar() {
    return assetDetailArguments.fromScanPage != AssetDetailSource.other;
  }

  @override
  void onReady() {
    super.onReady();
    initRouterParams();
    initPageDataOnFirstLoad();
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }

  /// 核心数据加载逻辑
  /// 包含所有数据初始化和刷新的核心逻辑
  Future<void> _loadAllData() async {
    // 加载资产信息
    await loadAssetData();

    await Future.wait([
      // 初始化资产类型列表
      initAssetTypeList(),
      // 初始化用户角色
      initUserRole(),
      // 初始化 Customize Logic
      initCustomizeLogic(),
      // 关联资产
      initRelationList(),
      // Schedule
      initScheduler(),
      // 页面空间状态
      // 保存按钮、打印按钮
      initPageWidgetStatus(),
      // Live 即时通讯
      initLiveTalk(),
    ]);

    // 此处依赖 LiveTalk 状态需要等待执行完成
    await initLayoutSettings();
  }

  /// 数据初始化（带Loading对话框）
  /// 用于保存操作后的数据刷新
  void initPageData() async {
    await _executeWithLoading(_loadAllData, showLoadingDialog: true);
  }

  /// 首次进入页面时的数据初始化（不显示Loading对话框）
  /// 只显示页面内的loading状态，不显示弹窗loading
  Future<void> initPageDataOnFirstLoad() async {
    await _executeWithLoading(_loadAllData, showLoadingDialog: true, errorMessage: '首次加载数据失败');
  }

  /// 静默刷新页面数据（显示Loading状态但不显示Loading对话框）
  /// 用于从其他页面返回时刷新数据，避免Loading对话框冲突
  Future<void> refreshPageDataSilently() async {
    await _executeWithLoading(_loadAllData, showLoadingDialog: false, errorMessage: '静默刷新数据失败');
  }

  /// 关闭页面
  void onCloseBtnClick() {
    closePage();
  }

  /// 保存按钮点击
  void onSaveBtnClick() async {
    CommonDialog.show(
      content: _SAVE_CONFIRMATION_MESSAGE,
      type: DialogType.info,
      cancelText: _CANCEL_TEXT,
      onConfirm: () async {
        _doSaveAsset();
      },
    );
  }

  /// 执行保存资产信息
  ///
  /// 1. 获取自定义视图控制器
  /// 2. 遍历所有资产数据,构建资产文本映射
  /// 3. 调用保存接口
  /// 4. 处理保存结果
  Future<void> _doSaveAsset() async {
    await _executeWithLoading(() async {
      // 获取自定义视图控制器
      final afCustomizeController = Get.find<AfCustomizeViewController>(tag: assetId.toString());

      // 校验所有项目
      if (!await afCustomizeController.validateAndShowErrors()) {
        return;
      }

      LogUtil.d('保存时logic：${jsCustomizeLogic.value?.jsSaveString ?? ''}');

      // 获取资产文本
      final assetTextMap = afCustomizeController.buildAssetTextMap();

      if (asset != null) {
        final assetTextJson = jsonEncode(assetTextMap);
        LogUtil.d(assetTextJson);
        final result = await assetDetailService.saveAsset(
          SaveAssetParams(
            assetId: assetId.toString(),
            assetTypeId: assetTypeId.toString(),
            assetText: assetTextJson,
            barcode: asset?.barcode,
            modifiedDate: modifiedDate,
            relationAssetIdList: relationState.relateListString.value,
          ),
        );

        _handleSaveResult(result);
      }
    });
  }

  /// 处理保存结果
  ///
  /// [result] - 保存结果
  void _handleSaveResult(dynamic result) {
    if (result.saveAsset.isSuccess()) {
      CommonDialog.showCustomToast(_UPDATE_COMPLETE_MESSAGE);
      initPageData();
    } else {
      CommonDialog.show(
        content: '${result.saveAsset.msg}',
        confirmText: _OK_TEXT,
        type: DialogType.error,
        onConfirm: () {
          Get.back();
        },
      );
    }
  }

  /// 页面来自 WF
  bool _isComeFromWf(AssetDetailSource fromSource) => fromSource == AssetDetailSource.wf;

  /// 加载资产详情
  Future<void> loadAssetData() async {
    final result = await assetDetailService.loadAssetById(LoadAssetByIdParams(assetId, fromScanPage.value));

    asset = result.asset;
    assetTypeId = result.assetTypeId ?? 0;
    assetTypeName = result.asset?.assetTypeName ?? '';
    assetName = result.asset?.assetName ?? '';
    assetText = result.assetText ?? '';
    modifiedDate = result.modifiedDate ?? '';

    final obj = jsonDecode(assetText);
    // 设置画面 Title
    uiState.pageTitle.value = obj[_ASSET_TYPE_KEY] ?? '';

    final param = FilterTalkParameter(
      assetTypeId: assetTypeId.toString(),
      assetIds: [assetId.toString()],
      assetTypeName: obj['資産種類'] ?? '',
      assetName: obj['assetName'] ?? '',
    );
    EventBus.fire(OverlayFiltertalkChangeEvent(param));
  }

  /// 执行处理设定二维码扫描
  Future<void> onActionQrcodeScan() {
    // TODO: implement actionQrcodeScan
    throw UnimplementedError();
  }

  /// 关闭页面
  void closePage() {
    final fromSource = fromScanPage;

    // 来源页面是 WF 直接调用返回即可
    if (_canDirectClose(fromSource)) {
      Get.back();
      return;
    }
    if (fromSource == AssetDetailSource.deeplink) {
      // 来源页面是 Action 则返回到资产列表

      final navigationService = Get.find<NavigationService>();
      navigationService.navigateOffAll(AutoRoutes.assetList, id: SharedNavBarEnum.assetList.navigatorId);
      return;
    }

    Get.back(id: SharedNavBarEnum.assetList.navigatorId);
  }

  bool _canDirectClose(AssetDetailSource fromSource) {
    return _isComeFromWf(fromSource) ||
        fromSource == AssetDetailSource.action ||
        fromSource == AssetDetailSource.scanPage;
  }

  /// 获取 CustomizeLogic
  Future<void> initCustomizeLogic() async {
    jsCustomizeLogic.value = await assetDetailService.initCustomizeLogic(assetTypeId);
  }

  /// 初始化布局设置
  /// - 获取布局设置
  /// - 获取资产类型列表
  Future<void> initLayoutSettings() async {
    final getLayoutSettingsResult = await assetDetailService.initLayoutSettings(
      assetTypeId,
      assetText,
      uiState.isAllowTalk.value,
    );
    if (getLayoutSettingsResult.isExeSuccess) {
      final assetDict = getLayoutSettingsResult.assetDict ?? {};
      uiState.assetDict.value = assetDict;
      uiState.appurtenancesInformationItemList.value = getLayoutSettingsResult.appurtenancesInformationItemList ?? [];
      uiState.rawAssetTypeItemList.value =
          getLayoutSettingsResult.rawAssetTypeItemList?.whereType<AssetItemListModel>().toList() ?? [];
    }
  }

  /// 初始化LiveTalk
  /// - 获取LiveTalkUrl
  /// - 获取LiveTalkListUrl
  /// - 获取TalkAvaliable
  Future<void> initLiveTalk() async {
    try {
      final liveTalkResult = await assetDetailService.getLiveTalkUrl(
        LiveTalkParams(assetId: assetId, assetTypeId: assetTypeId, assetTypeName: assetTypeName, assetName: assetName),
      );
      uiState.filterTalkParameterModel.value = liveTalkResult.filterTalkParameterModel;
      uiState.filterTalkParameterModel.refresh();
      uiState.liveTalkListUrl.value = liveTalkResult.liveTalkListUrl;
      uiState.isAllowTalk.value = await assetDetailService.isTalkAvailable();
    } catch (e) {
      LogUtil.d('initLiveTalk-$e');
      uiState.isAllowTalk.value = false;
      uiState.liveTalkListUrl.value = '';
    }
  }

  /// 初始化页面控件状态
  /// - 保存按钮
  /// - 打印按钮
  Future<void> initPageWidgetStatus() async {
    // 初始化保存按钮状态，在 wf 时不展示保存按钮
    uiState.showSaveBtn.value = !_isComeFromWf(fromScanPage);

    // 初始化打印按钮状态
    final assetTypeId = asset?.assetTypeId;
    if (assetTypeId != null) {
      uiState.printAvailable.value = await assetDetailService.getPrintAvailable(assetTypeId.toString());
    }
  }

  /// 初始化关联资产列表
  /// - 获取关联资产字典
  /// - 获取关联资产列表字符串
  /// - 获取编辑前关联资产数据
  Future<void> initRelationList() async {
    final getRelationListResult = await assetDetailService.initRelationList(assetId);
    if (getRelationListResult.isExeSuccess) {
      relationState.updateRelateDict(getRelationListResult.relateDict);
      relationState.updateRelateListString(getRelationListResult.relateListString);
      relationState.updateRelationDataBefore(getRelationListResult.relationDataBefore);
    }
  }

  /// 初始化路由参数
  void initRouterParams() {
    assetId = assetDetailArguments.assetId;
    jsExecutorContext.setData(JsExecutorContextConstants.assetId, assetId);
  }

  /// 初始化Schedule
  Future<void> initScheduler() async {
    final InitScheduleResult initScheduleResult = await assetDetailService.initScheduler(assetId, assetTypeId);
    uiState.hasSchedule.value = initScheduleResult.assetSchedulerInfo.appointmentList?.isNotEmpty ?? false;
    uiState.appointmentList.value =
        initScheduleResult.assetSchedulerInfo.appointmentList?.whereType<Appointment>().toList() ?? [];
    uiState.schedulerInfo.value = initScheduleResult.assetSchedulerInfo;
  }

  /// 切换 tab
  void onTabChange(int value) {
    uiState.currentPage.value = value;
    // 当切换到履历情报 tab 时隐藏扫描和保存按钮
    uiState.showScanBtn.value = value == _ASSET_INFO_TAB_INDEX;
    uiState.showSaveBtn.value = value == _ASSET_INFO_TAB_INDEX && !_isComeFromWf(fromScanPage);

    // 当切换到履历情报 tab 时，延迟初始化履历记录数量
    // 使用 addPostFrameCallback 确保在 build 完成后执行，避免在构建过程中访问 overlay context
    if (value == _HISTORY_TAB_INDEX) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        initHistoryRecordsCounts();
      });
    }
  }

  /// 显示 tab 切换确认对话框
  void showTabChangeDialog(int index) {
    showDialog(
      context: Get.context!,
      builder: (context) {
        return AlertDialog(
          title: const Text(_TAB_CHANGE_CONFIRMATION_TITLE),
          content: const Text(_TAB_CHANGE_CONFIRMATION_MESSAGE),
          actions: [
            TextButton(onPressed: Navigator.of(context).pop, child: const Text(_CANCEL_TEXT)),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // 用户确认后才触发切换
                onTabChange(index);
              },
              child: const Text(_MOVE_TEXT),
            ),
          ],
        );
      },
    );
  }

  /// 关联资产添加按钮点击
  void onRelationAddBtnClick() {}

  Future<void> initUserRole() async {
    await assetDetailService.getUserRole();
  }

  /// 处理履历记录点击事件
  ///
  /// 导航到资产历史页面，并处理返回的删除结果
  /// 当用户在历史页面删除记录后，会更新当前页面的记录计数显示
  Future<void> onAssetHistoryRecordsClick(AssetItemListModel item) async {
    final result = await navigationService.navigateTo(
      AutoRoutes.assetHistory,
      id: SharedNavBarEnum.assetList.navigatorId,
      arguments: AssetHistoryParams(
        item: item,
        assetId: assetId,
        assetTypeId: assetTypeId,
        isEditPermission: _getEditPermissions(item),
        fromPage: AssetHistorySourceEnum.asset,
      ),
    );

    await _handleAssetHistoryDeleteResult(result);
  }

  bool _getEditPermissions(AssetItemListModel item) =>
      item.isEditPermissions == _EDIT_PERMISSION_ENABLED || (item.isEditPermissions?.isEmpty ?? true);

  /// 处理从资产历史页面返回的删除结果
  ///
  /// 采用乐观更新策略：先立即更新UI显示，然后通过API验证数据一致性
  /// 这种方式既保证了用户体验，又确保了数据的最终一致性
  ///
  /// [result] 从资产历史页面返回的结果，可能是删除结果对象或null
  Future<void> _handleAssetHistoryDeleteResult(dynamic result) async {
    // 检查返回结果是否为删除结果对象
    if (result is AssetHistoryDeleteResult && result.isSuccess) {
      LogUtil.d('收到资产历史删除结果: $result');
      LogUtil.d('使用乐观更新策略进行数据同步');

      try {
        // 第一步：乐观更新 - 立即更新UI显示，提供即时反馈
        await _performOptimisticUpdate(result);

        // 第二步：API验证 - 根据配置决定是否进行异步验证
        if (_ENABLE_ASYNC_VERIFICATION) {
          _performAsyncDataVerification(result.appurtenancesInformationTypeId);
        }

        LogUtil.d('删除结果处理完成');
      } catch (e, stackTrace) {
        LogUtil.e('处理删除结果时发生异常: $e', stackTrace: stackTrace);

        // 如果处理失败，直接重新加载所有历史记录数量
        LogUtil.d('处理删除结果失败，重新加载所有历史记录数量');
        await initHistoryRecordsCounts();
      }
    } else if (result != null) {
      LogUtil.d('收到非删除结果的返回值: ${result.runtimeType}');
    }
  }

  /// 执行乐观更新
  ///
  /// 立即更新UI显示，为用户提供即时反馈
  /// 这是第一阶段的更新，优先考虑用户体验
  ///
  /// [result] 删除操作的结果对象
  Future<void> _performOptimisticUpdate(AssetHistoryDeleteResult result) async {
    // 查找对应的历史记录项目并更新计数
    final index = uiState.appurtenancesInformationItemList.indexWhere(
      (item) => item.optionObject?.appurtenancesInformationTypeId == result.appurtenancesInformationTypeId,
    );

    if (index != -1) {
      // 更新对应项目的数据条数
      final item = uiState.appurtenancesInformationItemList[index];
      final oldCount = item.dataCounts ?? 0;
      item.dataCounts = result.remainingCount;

      // 触发UI更新
      uiState.appurtenancesInformationItemList[index] = item;

      LogUtil.d(
        '乐观更新历史记录计数: 类型ID=${result.appurtenancesInformationTypeId}, '
        '原计数=$oldCount, 新计数=${result.remainingCount}',
      );
    } else {
      LogUtil.w('未找到对应的历史记录类型: ${result.appurtenancesInformationTypeId}');
      throw Exception('未找到对应的历史记录类型');
    }
  }

  /// 执行异步数据验证
  ///
  /// 通过API重新获取最新的计数数据，确保数据一致性
  /// 这是第二阶段的更新，优先考虑数据可靠性
  ///
  /// [appurtenancesInformationTypeId] 需要验证的历史记录类型ID
  void _performAsyncDataVerification(int appurtenancesInformationTypeId) {
    // 使用异步方式验证数据，不阻塞用户操作
    Future.delayed(const Duration(milliseconds: _VERIFICATION_DELAY_MS), () async {
      try {
        LogUtil.d('开始验证历史记录计数数据一致性: 类型ID=$appurtenancesInformationTypeId');

        // 重新获取指定类型的历史记录计数
        await loadAssetHistoryRecordsCount(appurtenancesInformationTypeId.toString());

        LogUtil.d('历史记录计数验证完成: 类型ID=$appurtenancesInformationTypeId');
      } catch (e, stackTrace) {
        LogUtil.e('验证历史记录计数时发生异常: $e', stackTrace: stackTrace);

        // 根据配置决定是否显示验证错误
        if (_SHOW_VERIFICATION_ERRORS) {
          CommonDialog.show(content: 'データの整合性確認に失敗しました。', confirmText: 'OK', type: DialogType.error);
        } else {
          // 静默处理，避免影响用户体验
          LogUtil.w('历史记录计数验证失败，但不影响用户操作');
        }
      }
    });
  }

  /// 初始化履历记录数量
  Future<void> initHistoryRecordsCounts() async {
    // 检查履历情报列表是否已经初始化
    if (uiState.appurtenancesInformationItemList.isEmpty) {
      return;
    }

    await _executeWithLoading(() async {
      // 使用 Future.wait 并发请求所有履历记录数量
      final futures = uiState.appurtenancesInformationItemList
          .where((item) => item.itemId != null)
          .map(
            (item) => loadAssetHistoryRecordsCount(item.optionObject?.appurtenancesInformationTypeId?.toString() ?? ''),
          );

      await Future.wait(futures);
    });
  }

  ///　获取履历情报数据条数
  Future<void> loadAssetHistoryRecordsCount(String appurtenancesInformationTypeId) async {
    final result = await assetDetailService.getAssetHistoryRecordsCount(
      GetAssetHistoryRecordsParams(assetId, appurtenancesInformationTypeId),
    );

    // 更新对应项目的数据条数
    final index = uiState.appurtenancesInformationItemList.indexWhere(
      (item) => item.optionObject?.appurtenancesInformationTypeId?.toString() == appurtenancesInformationTypeId,
    );
    if (index != -1) {
      final item = uiState.appurtenancesInformationItemList[index];
      item.dataCounts = result.count;
      // 触发更新
      uiState.appurtenancesInformationItemList[index] = item;
    }
  }

  /// 初始化资产类型列表
  Future<void> initAssetTypeList() async {
    final result = await assetDetailService.getAssetTypeList();
    uiState.assetTypeList.value = result.assetTypeList;
  }

  /// 导航到资产日程详情页面。
  ///
  /// 此方法使用 `navigationService` 导航到由 `AutoRoutes.assetSchedule` 定义的
  /// 资产日程详情页面。传入的 appointment 会被克隆，避免内部修改直接影响当前页面的数据。
  /// 当内部保存成功后，会通过回调函数重新刷新整个资产详细页面的所有数据。
  ///
  /// [appointment] - 包含要查看的日程详细信息的预约模型。
  void navigateToScheduleDetail(Appointment appointment) {
    navigationService.navigateTo(
      AutoRoutes.assetSchedule,
      arguments: AssetScheduleArgument(
        appointment: appointment.clone(), // 克隆 appointment 避免内部修改影响原始数据
        scheduleData: uiState.schedulerInfo.value,
        onSaveSuccess: (updatedAppointment) {
          // 保存成功后的回调，静默刷新整个资产详细页面的所有数据
          // 使用静默刷新避免与schedule页面的Loading对话框冲突
          refreshPageDataSilently();
        },
      ),
    );
  }
}
